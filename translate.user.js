// ==UserScript==
// @name         New Userscript
// @namespace    http://tampermonkey.net/
// @version      2025-08-23
// @description  try to take over the world!
// <AUTHOR>
// @match        *://*/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=translate.google.com
// @grant        GM_xmlhttpRequest
// ==/UserScript==

;(async function () {
	/**
	 * JavaScript implementation of Google Translate API (unofficial)
	 * Mimics the behavior of the googletrans Python library
	 */

	class GoogleTranslator {
		constructor() {
			this.serviceUrl = "translate.googleapis.com"
			this.clientType = "gtx" // Use client API (no token required)
			this.userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
		}

		/**
		 * Build URL parameters for the translation request
		 */
		buildParams(text, srcLang = "auto", destLang = "en") {
			const params = new URLSearchParams({
				client: this.clientType,
				sl: srcLang, // source language
				tl: destLang, // target language
				hl: destLang, // UI language
				ie: "UTF-8",
				oe: "UTF-8",
				otf: "1",
				ssel: "0",
				tsel: "0",
				tk: "xxxx", // dummy token for client API
				q: text,
			})

			// Add dt parameters (data types to return)
			const dtParams = ["at", "bd", "ex", "ld", "md", "qca", "rw", "rm", "ss", "t"]
			dtParams.forEach(dt => params.append("dt", dt))

			return params
		}

		/**
		 * Parse the JSON response from Google Translate
		 */
		parseResponse(responseText) {
			try {
				// Clean up malformed JSON (Google returns weird format)
				let cleanText = responseText

				// Replace multiple commas with null values
				while (cleanText.includes(",,")) {
					cleanText = cleanText.replace(",,", ",null,")
				}
				while (cleanText.includes("[,")) {
					cleanText = cleanText.replace("[,", "[null,")
				}

				const data = JSON.parse(cleanText)
				return data
			} catch (error) {
				console.error("Failed to parse response:", error)
				throw new Error("Invalid response from Google Translate")
			}
		}

		/**
		 * Extract translation from the parsed response
		 */
		extractTranslation(data) {
			if (!data || !Array.isArray(data) || !data[0]) {
				throw new Error("Invalid translation data")
			}

			// Extract translated text from response
			let translatedText = ""
			if (Array.isArray(data[0])) {
				translatedText = data[0].map(segment => (segment && segment[0] ? segment[0] : "")).join("")
			}

			// Extract detected source language
			let detectedLang = "unknown"
			try {
				if (data[2]) {
					detectedLang = data[2]
				}
			} catch (e) {
				// Ignore errors in language detection
			}

			// Extract confidence score for language detection
			let confidence = 0
			try {
				if (data[8] && data[8][0]) {
					if (Array.isArray(data[8][0])) {
						detectedLang = data[8][0][0] || detectedLang
					} else {
						detectedLang = data[8][0] || detectedLang
					}

					if (data[8][-2]) {
						confidence = data[8][-2]
					} else if (data[8][data[8].length - 2]) {
						confidence = data[8][data[8].length - 2]
					}
				}
			} catch (e) {
				// Ignore errors in confidence extraction
			}

			return {
				translatedText,
				detectedLang,
				confidence,
			}
		}

		/**
		 * Make HTTP request to Google Translate API
		 */
		async makeRequest(text, srcLang = "auto", destLang = "en") {
			const params = this.buildParams(text, srcLang, destLang)
			const url = `https://${this.serviceUrl}/translate_a/single?${params.toString()}`

			return new Promise((resolve, reject) => {
				GM_xmlhttpRequest({
					method: "GET",
					url: url,
					headers: {
						"User-Agent": this.userAgent,
						Accept: "*/*",
						"Accept-Language": "en-US,en;q=0.9",
						"Accept-Encoding": "gzip, deflate, br",
						Connection: "keep-alive",
						"Upgrade-Insecure-Requests": "1",
					},
					onload: function (response) {
						if (response.status >= 200 && response.status < 300) {
							resolve(response.responseText)
						} else {
							reject(new Error(`HTTP error! status: ${response.status}`))
						}
					},
					onerror: function (error) {
						reject(new Error(`Network error: ${error}`))
					},
					ontimeout: function () {
						reject(new Error("Request timeout"))
					},
				})
			})
		}

		/**
		 * Detect the language of the given text
		 */
		async detect(text) {
			try {
				const responseText = await this.makeRequest(text, "auto", "en")
				const data = this.parseResponse(responseText)
				const result = this.extractTranslation(data)

				return {
					language: result.detectedLang,
					confidence: result.confidence,
				}
			} catch (error) {
				console.error("Language detection failed:", error)
				throw error
			}
		}

		/**
		 * Translate text from source language to destination language
		 */
		async translate(text, srcLang = "auto", destLang = "en") {
			try {
				const responseText = await this.makeRequest(text, srcLang, destLang)
				const data = this.parseResponse(responseText)
				const result = this.extractTranslation(data)

				return {
					originalText: text,
					translatedText: result.translatedText,
					sourceLanguage: result.detectedLang,
					targetLanguage: destLang,
					confidence: result.confidence,
				}
			} catch (error) {
				console.error("Translation failed:", error)
				throw error
			}
		}
	}

	/**
	 * Main function that detects language and translates to English
	 */
	async function translateToEnglish(text) {
		const translator = new GoogleTranslator()

		try {
			// First detect the language
			console.log("Detecting language...")
			const detection = await translator.detect(text)
			console.log(`Detected language: ${detection.language} (confidence: ${detection.confidence})`)

			// If already in English, return as-is
			if (detection.language === "en") {
				return {
					originalText: text,
					translatedText: text,
					sourceLanguage: "en",
					targetLanguage: "en",
					confidence: detection.confidence,
					message: "Text is already in English",
				}
			}

			// Translate to English
			console.log("Translating to English...")
			const translation = await translator.translate(text, detection.language, "en")

			return translation
		} catch (error) {
			console.error("Translation process failed:", error)
			throw error
		}
	}

	/**
	 * Extract all text content from the page
	 */
	function extractPageText() {
		// Get all text nodes, excluding script and style elements
		const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
			acceptNode: function (node) {
				// Skip script, style, and other non-visible elements
				const parent = node.parentElement
				if (!parent) return NodeFilter.FILTER_REJECT

				const tagName = parent.tagName.toLowerCase()
				if (["script", "style", "noscript", "meta", "head"].includes(tagName)) {
					return NodeFilter.FILTER_REJECT
				}

				// Skip empty or whitespace-only text
				if (!node.textContent.trim()) {
					return NodeFilter.FILTER_REJECT
				}

				return NodeFilter.FILTER_ACCEPT
			},
		})

		const textNodes = []
		let node
		while ((node = walker.nextNode())) {
			textNodes.push(node)
		}

		return textNodes
	}

	/**
	 * Translate all text nodes on the page
	 */
	async function translatePage() {
		const button = document.getElementById("translate-button")
		const originalText = button.textContent

		try {
			button.textContent = "Translating..."
			button.disabled = true

			const textNodes = extractPageText()
			console.log(`Found ${textNodes.length} text nodes to translate`)

			// Group text nodes by their content to avoid duplicate translations
			const textGroups = new Map()
			textNodes.forEach(node => {
				const text = node.textContent.trim()
				if (text.length > 0) {
					if (!textGroups.has(text)) {
						textGroups.set(text, [])
					}
					textGroups.get(text).push(node)
				}
			})

			// Translate each unique text
			let translated = 0
			for (const [originalText, nodes] of textGroups) {
				try {
					const result = await translateToEnglish(originalText)

					// Update all nodes with the same original text
					nodes.forEach(node => {
						node.textContent = result.translatedText
					})

					translated++
					button.textContent = `Translating... (${translated}/${textGroups.size})`

					// Small delay to avoid overwhelming the API
					await new Promise(resolve => setTimeout(resolve, 100))
				} catch (error) {
					console.error(`Failed to translate: "${originalText}"`, error)
				}
			}

			button.textContent = "Translation Complete!"
			setTimeout(() => {
				button.textContent = originalText
				button.disabled = false
			}, 2000)
		} catch (error) {
			console.error("Page translation failed:", error)
			button.textContent = "Translation Failed"
			setTimeout(() => {
				button.textContent = originalText
				button.disabled = false
			}, 2000)
		}
	}

	/**
	 * Create and add the translate button overlay
	 */
	function createTranslateButton() {
		// Create button element
		const button = document.createElement("button")
		button.id = "translate-button"
		button.textContent = "Translate Page"

		// Style the button
		button.style.cssText = `
			position: fixed;
			top: 10px;
			left: 10px;
			z-index: 10000;
			background: #4285f4;
			color: white;
			border: none;
			border-radius: 6px;
			padding: 10px 16px;
			font-size: 14px;
			font-weight: 500;
			cursor: pointer;
			box-shadow: 0 2px 8px rgba(0,0,0,0.2);
			transition: all 0.2s ease;
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		`

		// Add hover effects
		button.addEventListener("mouseenter", () => {
			button.style.background = "#3367d6"
			button.style.transform = "translateY(-1px)"
			button.style.boxShadow = "0 4px 12px rgba(0,0,0,0.3)"
		})

		button.addEventListener("mouseleave", () => {
			button.style.background = "#4285f4"
			button.style.transform = "translateY(0)"
			button.style.boxShadow = "0 2px 8px rgba(0,0,0,0.2)"
		})

		// Add click handler
		button.addEventListener("click", translatePage)

		// Add button to page
		document.body.appendChild(button)
	}

	// Initialize the translate button when page loads
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", createTranslateButton)
	} else {
		createTranslateButton()
	}
})()
